<?php

namespace backendapi\services\promote\transfermoneyv2;

use backendapi\models\promote\AdsMainBody;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use backendapi\services\promote\transfermoneyv2\validator\TimeValidator;
use backendapi\services\promote\transfermoneyv2\validator\AccountValidator;
use backendapi\services\promote\transfermoneyv2\validator\AmountValidator;
use backendapi\services\promote\transfermoneyv2\validator\TransferValidator;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use common\components\promoteData\Adq;
use common\components\promoteData\Oceanengine;
use common\enums\reportEnum;
use common\helpers\BcHelper;
use common\models\common\AdsAccountSub;
use common\models\promote\AdsTransferMoneyRecord;
use common\queues\TransferMoneyJobV2;
use Exception;
use Yii;

/**
 * 统一业务服务类 TransferMoneyServiceV2
 * 
 * 整合所有前面实现的组件，基于现有 TransferMoneyBatchService 的业务逻辑
 * 支持四种充值模式：正常充值、批量充值、定时充值、加粉充值
 * 
 * 核心特性：
 * - 使用平台适配器模式支持多平台
 * - 使用验证器链进行数据验证
 * - 使用缓存管理器管理充值限制
 * - 保持与现有系统的完全兼容性
 */
class TransferMoneyServiceV2
{
    /**
     * @var PlatformFactory 平台工厂
     */
    private $platformFactory;

    /**
     * @var TransferCacheManager 缓存管理器
     */
    private $cacheManager;

    /**
     * @var TransferValidator 验证器
     */
    private $validator;

    /**
     * @var PlatformAdapterInterface 当前平台适配器
     */
    private $currentAdapter;

    // 业务属性 - 基于现有 TransferMoneyBatchService
    private $accessToken = '';
    public $advertiser_id = '';
    private $target_advertiser_id = '';
    private $target_advertiser_name = '';
    private $amount = 0;
    private $transaction_seq = '';
    private $user_id;
    private $user_name;
    public $mainBody;
    public $platform = '';
    public $organization_id = '';
    public $insufficientBalance;

    // 平台限额配置
    private $tiktok_single_recharge_amount = 1000;
    private $adq_single_recharge_amount = 2000;
    private $tiktok_one_hour_max_recharge_amount = 3000;
    private $adq_one_hour_max_recharge_amount = 20000;

    // 错误码体系 - 保持与现有系统一致
    public $code = 422;
    public $success_code = 200;
    private $time_code = 100;
    public $success_insufficient_balance_code = 201;
    private $error_code_it = 422;
    private $error_code_promote = 423;
    private $error_code_insufficient_balance = 424;

    // 缓存相关
    private $redisCacheData = [];
    private $timeRecharge = '';

    /**
     * 构造函数
     * 
     * @param PlatformFactory|null $platformFactory 平台工厂
     * @param TransferCacheManager|null $cacheManager 缓存管理器
     */
    public function __construct(
        PlatformFactory $platformFactory = null,
        TransferCacheManager $cacheManager = null
    ) {
        $this->platformFactory = $platformFactory ?: new PlatformFactory();
        $this->cacheManager = $cacheManager ?: new TransferCacheManager();
        $this->validator = TransferValidator::createDefault();
    }

    /**
     * 主入口方法 - 基于现有 TransferMoneyBatchService::run() 方法逻辑
     * 
     * @param array $params 充值参数
     * @return array|string 充值结果
     * @throws Exception
     */
    public function run($params)
    {
        // 1. 时间限制检查
        $this->timeLimit();
        
        // 2. 参数处理
        $result = $this->dealParams($params);

        // 3. 账户验证
        $data['target_advertiser_ids'] = $this->verificationAccount($result);
        
        // 4. 金额验证
        $data['amount'] = $this->verificationAmount($result);

        // 5. 定时充值判断
        $this->isTimeRecharge($result);
        $data['user_name'] = $this->user_name;
        
        if ($this->timeRecharge) {
            // 定时充值流程
            $this->code = $this->time_code;
            $data['isTimeRecharge'] = true;
            $data['execute_time'] = $this->timeRecharge;
            TransferMoneyJobV2::addJob($data);
            return '定时充值操作成功';
        } else {
            // 立即充值流程
            $this->code = $this->success_code;
            $data['execute_time'] = time();
            $data['isTimeRecharge'] = false;
            TransferMoneyJobV2::addJob($data);
            return ['200' => ['code' => $this->success_code, 'msg' => '充值成功']];
        }
    }

    /**
     * 执行充值逻辑 - 基于现有 TransferMoneyBatchService::execute() 方法逻辑
     * 
     * @param array $data 充值数据
     * @return array 充值结果
     * @throws Exception
     */
    public function execute($data)
    {
        $total = count($data['target_advertiser_ids']);
        
        // 批量限制检查
        if ($total > 50) {
            $this->code = $this->error_code_promote;
            throw new Exception('充值一次最多只能50个户');
        }

        $this->amount = $data['amount'];
        $result = [];
        $num = 1;

        foreach ($data['target_advertiser_ids'] as $target_advertiser_id) {
            // 初始化
            $this->initialize();
            
            // 频率控制 - 每充值10个户睡眠500毫秒
            if ($num % 10 === 0) {
                usleep(500000);
            }

            try {
                // 目标账户设置
                $this->setTargetAdvertiserIds($target_advertiser_id);
                
                // 金额限制检查
                $this->amountLimit();
                
                // 执行充值
                $this->transferMoney();
                
                $num++;
                
                // 成功处理
                $this->success();
                $msg = '充值成功';
                
            } catch (Exception $e) {
                $msg = $e->getMessage();
            }

            $result[$this->code][] = [
                'msg' => $msg,
                'target_advertiser_id' => $this->target_advertiser_id,
                'target_advertiser_name' => $this->target_advertiser_name,
                'main_body' => $this->mainBody,
                'advertiser_id' => $this->advertiser_id,
                'insufficientNalance' => $this->insufficientBalance
            ];
        }

        return $result;
    }

    /**
     * 单个账户充值 - 基于现有 TransferMoneyBatchService::transferMoney() 方法逻辑
     * 
     * @return bool 充值是否成功
     * @throws Exception
     */
    public function transferMoney()
    {
        $balance = $this->getBalance();
        $this->insufficientBalance = $balance - $this->amount;

        // 获取平台适配器
        $adapter = $this->getCurrentAdapter();
        
        if ($this->platform == reportEnum::TIKTOL) {
            // 抖音平台充值
            $result = Oceanengine::transferCreate(
                $this->accessToken,
                $this->organization_id,
                $this->advertiser_id,
                $this->target_advertiser_id,
                $this->amount
            );
            
            if (!isset($result['code']) || $result['code'] != 0) {
                $this->code = $this->error_code_it;
                throw new Exception('创建转账交易号：' . $this->advertiser_id . ' 报错：' . $result['message'] . ',请稍后重新提交');
            }
            
        } elseif ($this->platform == reportEnum::ADQ) {
            // ADQ平台充值
            $result = Adq::subcustomerTransfer(
                $this->accessToken,
                $this->advertiser_id,
                $this->target_advertiser_id,
                $this->amount
            );
            
            if ($result['code'] != 0) {
                $this->code = $this->error_code_it;
                throw new Exception('充值失败：' . $result['message_cn']);
            }
            
        } else {
            $this->code = $this->error_code_it;
            throw new Exception('充值失败：充值平台不存在，请联系信息部处理');
        }

        // 更新余额缓存
        $this->cacheManager->setBalance($this->advertiser_id, $this->insufficientBalance);
        
        return true;
    }

    /**
     * 获取账户余额
     * 
     * @return float 账户余额
     * @throws Exception
     */
    public function getBalance()
    {
        // 先尝试从缓存获取
        $balance = $this->cacheManager->getBalance($this->advertiser_id);
        
        if ($balance === null) {
            if ($this->platform == reportEnum::TIKTOL) {
                // 抖音平台余额查询
                $res = Oceanengine::getFund($this->accessToken, $this->advertiser_id);
                if (!isset($res['code']) || $res['code'] != 0) {
                    $this->code = $this->error_code_it;
                    throw new Exception('查询户' . $this->advertiser_id . '可用余额报错:' . $res['message']);
                }
                $balance = $res['data']['balance'];
                
            } elseif ($this->platform == reportEnum::ADQ) {
                // ADQ平台余额查询
                $data = Adq::getBalance($this->accessToken, $this->advertiser_id, '', '');
                $balance = 0;
                foreach ($data as $item) {
                    $balance = BcHelper::add($balance, $item['balance']);
                }
                $balance = BcHelper::div($balance, 100);
                
            } else {
                $this->code = $this->error_code_it;
                throw new Exception('查询户' . $this->advertiser_id . '，充值平台不存在，请联系信息部处理');
            }
            
            // 缓存余额
            $this->cacheManager->setBalance($this->advertiser_id, $balance);
        }

        // 余额不足检查
        if ($this->amount > $balance) {
            // 删除缓存
            $this->cacheManager->deleteBalance($this->advertiser_id);
            $this->code = $this->error_code_insufficient_balance;
            
            $errorMsg = '充值失败，主体：' . $this->mainBody . '（' . $this->advertiser_id . '）， ' . 
                       '账户余额不足，剩余：' . $balance . '，请联系推广管理人员处理';
            throw new Exception($errorMsg);
        }

        return $balance;
    }

    /**
     * 查询所有账户余额
     * 
     * @return string 账户余额信息
     * @throws Exception
     */
    public function getAccountBalance()
    {
        try {
            $list = $this->accountList();
            if (empty($list)) {
                throw new Exception('要查询的账户不能为空');
            }

            $content = '';
            foreach ($list as $name => $account) {
                $this->advertiser_id = $account;
                $this->mainBody = $name;
                $this->setToken();
                $this->setPlatform();
                
                if ($this->platform == reportEnum::TIKTOL) {
                    $res = Oceanengine::getFund($this->accessToken, $this->advertiser_id);
                    if (!isset($res['code']) || $res['code'] != 0) {
                        throw new Exception('查询户' . $this->advertiser_id . '可用余额报错:' . $res['message']);
                    }
                    $balance = $res['data']['balance'];
                    $content .= $name . '：' . $balance . PHP_EOL;
                    
                } elseif ($this->platform == reportEnum::ADQ) {
                    $data = Adq::getBalance($this->accessToken, $this->advertiser_id, '', '');
                    $balance = 0;
                    foreach ($data as $item) {
                        $balance = BcHelper::add($balance, $item['balance']);
                    }
                    $balance = BcHelper::div($balance, 100);
                    $content .= $name . '：' . $balance . PHP_EOL;
                    
                } else {
                    $this->code = $this->error_code_it;
                    throw new Exception('查询户' . $this->advertiser_id . '，充值平台不存在，请联系信息部处理');
                }
            }

            return $content;
            
        } catch (Exception $e) {
            throw new Exception('查询余额失败，原因：' . $e->getMessage());
        }
    }

    /**
     * 初始化 - 基于现有 TransferMoneyBatchService::initialize() 方法逻辑
     */
    public function initialize()
    {
        $this->target_advertiser_id = '';
        $this->target_advertiser_name = '';
        $this->mainBody = '';
        $this->advertiser_id = '';
        $this->accessToken = '';
        $this->code = 422;
        $this->redisCacheData = [];
        $this->insufficientBalance = '';
        $this->transaction_seq = '';
    }

    /**
     * 时间限制检查
     * 
     * @throws Exception
     */
    public function timeLimit()
    {
        $currentTime = date('H:i');
        $startTime = '02:00';
        $endTime = '06:30';

        if ($currentTime >= $startTime && $currentTime <= $endTime) {
            $this->code = $this->error_code_promote;
            throw new Exception('"凌晨2点到6点30分"时间段不可充值');
        }
    }

    /**
     * 参数处理 - 基于现有 TransferMoneyBatchService::dealParams() 方法逻辑
     * 
     * @param array $params 输入参数
     * @return array 处理后的参数
     * @throws Exception
     */
    public function dealParams($params = '')
    {
        if (empty($params)) {
            $this->code = $this->error_code_promote;
            throw new Exception('充值数据不能为空');
        }

        $this->user_id = $params['user_id'];
        $this->user_name = $params['user_name'];

        $parts = explode("\n", $params['data']);
        $list = [];
        $keyList = [];
        
        foreach ($parts as $part) {
            if (strpos($part, '：') === false) {
                continue;
            }
            list($key, $value) = explode('：', $part, 2);
            $key = trim($key);
            $value = trim($value);
            $list[$key] = $value;
            $keyList[] = $key;
        }
        
        $fields = ['账户ID', '转账金额'];
        $diff = array_diff($fields, $keyList);
        if (!empty($diff)) {
            $this->code = $this->error_code_promote;
            throw new Exception('数据格式有误，请认真审查');
        }

        return $list;
    }

    /**
     * 账户验证
     * 
     * @param array $data 验证数据
     * @return array 验证后的账户ID列表
     * @throws Exception
     */
    public function verificationAccount($data)
    {
        $target_advertiser_ids = explode('、', $data['账户ID']);
        $target_advertiser_ids = array_filter($target_advertiser_ids, function ($value) {
            return $value !== null && $value !== '';
        });

        $target_advertiser_ids = array_unique($target_advertiser_ids);

        if (empty($target_advertiser_ids)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户ID不能为空');
        }

        // 检查平台一致性
        $platform = AdsAccountSub::find()->alias('as')
            ->select('a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $target_advertiser_ids])
            ->groupBy('a.platform')->asArray()->all();

        if (count($platform) > 1) {
            $this->code = $this->error_code_promote;
            throw new Exception('不允许多平台账户充值');
        }

        $this->platform = $platform[0]['platform'];

        return $target_advertiser_ids;
    }

    /**
     * 金额验证
     * 
     * @param array $data 验证数据
     * @return float 验证后的金额
     * @throws Exception
     */
    public function verificationAmount($data)
    {
        $amount = (float)$data['转账金额'];
        
        if ($amount <= 0) {
            $this->code = $this->error_code_promote;
            throw new Exception('单次充值金额必须大于0');
        }

        if ($this->platform == reportEnum::ADQ) {
            $maxAmount = $this->adq_single_recharge_amount;
        } else {
            $maxAmount = $this->tiktok_single_recharge_amount;
        }

        if ($amount > $maxAmount) {
            $this->code = $this->error_code_promote;
            throw new Exception('单次充值金额不得超过' . $maxAmount);
        }

        return $amount;
    }

    /**
     * 定时充值判断 - 基于现有 TransferMoneyBatchService::isTimeRecharge() 方法逻辑
     * 
     * @param array $data 验证数据
     * @throws Exception
     */
    public function isTimeRecharge($data)
    {
        if (!isset($data['定时充值'])) {
            return;
        }

        $timeRechargeDate = $data['定时充值'];

        if (empty($timeRechargeDate)) {
            $this->code = $this->error_code_promote;
            throw new Exception('定时充值时间不能为空');
        }

        $timeRecharge = strtotime($timeRechargeDate);
        if ($timeRecharge <= time()) {
            $this->code = $this->error_code_promote;
            throw new Exception('定时充值时间不能小于当前时间');
        }

        $currentDate = date('Y-m-d', time());
        if ($timeRecharge >= strtotime($currentDate . '+2 day')) {
            $this->code = $this->error_code_promote;
            throw new Exception('定时充值时间只能在今天和明天之间');
        }

        $this->timeRecharge = $timeRecharge;
    }

    /**
     * 设置目标账户 - 基于现有 TransferMoneyBatchService::setTargetAdvertiserIds() 方法逻辑
     * 
     * @param string $target_advertiser_id 目标账户ID
     * @throws Exception
     */
    public function setTargetAdvertiserIds($target_advertiser_id)
    {
        $this->target_advertiser_id = $target_advertiser_id;
        
        $info = AdsMainBody::find()
            ->alias('mb')->select('aas.id,mb.name,mb.sub_advertiser_id,aas.sub_advertiser_name')
            ->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.main_body_id = mb.id')
            ->where(['aas.sub_advertiser_id' => $this->target_advertiser_id])
            ->asArray()
            ->one();
            
        if (empty($info)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户ID不存在系统中，请核对下账户ID是否正确');
        }

        $this->mainBody = $info['name'];
        if (empty($this->mainBody)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户主体未绑定，请联系管理员绑定！');
        }
        
        $this->advertiser_id = $info['sub_advertiser_id'];
        $this->target_advertiser_name = $info['sub_advertiser_name'];
        $this->setToken();
        $this->setPlatform();
    }

    /**
     * 设置访问令牌
     * 
     * @throws Exception
     */
    public function setToken()
    {
        $info = AdsAccountSub::find()->alias('as')
            ->select('a.access_token,a.advertiser_id as organization_id,a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $this->advertiser_id])
            ->asArray()
            ->one();

        if (empty($info)) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '不存在erp系统中，请联系技术部处理');
        }

        if (empty($info['access_token'])) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '的token不存在erp系统中，请联系技术部处理');
        }

        if (empty($info['organization_id']) && $info['platform'] == reportEnum::TIKTOL) {
            $this->code = $this->error_code_it;
            throw new Exception('该备用金户：' . $this->advertiser_id . '的组织ID不存在erp系统中，请联系技术部处理');
        }

        $this->accessToken = $info['access_token'];
        $this->organization_id = $info['organization_id'];
    }

    /**
     * 设置平台
     * 
     * @throws Exception
     */
    public function setPlatform()
    {
        $platform = AdsAccountSub::find()->alias('as')
            ->select('a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $this->advertiser_id])
            ->scalar();

        if (empty($platform)) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '的平台不存在erp系统中，请联系技术部处理');
        }

        $this->platform = $platform;
    }

    /**
     * 金额限制检查
     * 
     * @return bool
     * @throws Exception
     */
    public function amountLimit()
    {
        if ($this->platform == reportEnum::ADQ) {
            $maxTotalAmount = $this->adq_one_hour_max_recharge_amount;
        } else {
            $maxTotalAmount = $this->tiktok_one_hour_max_recharge_amount;
        }

        return $this->cacheManager->checkHourlyLimit(
            $this->target_advertiser_id,
            $this->amount,
            $maxTotalAmount
        );
    }

    /**
     * 成功处理
     */
    public function success()
    {
        // 记录到Redis缓存（保持原有逻辑）
        $this->cacheManager->recordSuccessfulTransfer(
            $this->target_advertiser_id,
            $this->amount,
            $this->user_name
        );

        // 保存充值记录到数据库
        $this->saveTransferRecord();

        if ($this->insufficientBalance <= 3000) {
            $this->code = $this->success_insufficient_balance_code;
        } else {
            $this->code = $this->success_code;
        }
    }

    /**
     * 获取账户列表
     * 
     * @return array
     */
    public function accountList()
    {
        $key = AdsMainBody::$reidsKey;
        $redis = Yii::$app->cache;
        $list = $redis->get($key);
        
        if ($list) {
            return $list;
        }

        $list = AdsMainBody::find()
            ->select('name,sub_advertiser_id')
            ->where(['<>', 'sub_advertiser_id', ''])
            ->andWhere(['status' => 1])
            ->asArray()->all();

        $list = array_column($list, 'sub_advertiser_id', 'name');
        $redis->set($key, $list, 3600);
        
        return $list;
    }

    /**
     * 结果数据处理 - 基于现有 TransferMoneyBatchService::resRealData() 方法逻辑
     * 
     * @param array $res 原始结果数据
     * @return array 处理后的结果数据
     */
    public function resRealData($res)
    {
        $content = [];
        
        foreach ($res as $code => $v) {
            if ($code == 200) {
                $content[$code] = '充值成功';
            }
            
            if ($code == 201) {
                $mainBodyBalances = [];
                foreach ($v as $entry) {
                    $mainBody = $entry['main_body'] ?? '';
                    $balance = $entry['insufficientNalance'] ?? null;
                    $advertiserId = $entry['advertiser_id'] ?? '';

                    if ($mainBody && $balance !== null) {
                        if (!isset($mainBodyBalances[$mainBody])) {
                            $mainBodyBalances[$mainBody] = [
                                '余额' => $balance,
                                'advertiser_id' => $advertiserId,
                            ];
                        } else {
                            $mainBodyBalances[$mainBody]['余额'] = min($mainBodyBalances[$mainBody]['余额'], $balance);
                        }
                    }
                }
                
                $index = 0;
                $total = count($mainBodyBalances);
                foreach ($mainBodyBalances as $mainBody => $data) {
                    $content[$code] .= '主体：' . $mainBody . ',账户ID：' . $data['advertiser_id'] . 
                                     ' 的备用金仅剩：' . $data['余额'] . '元，请及时充值';
                    if ($index < $total) {
                        $content[$code] .= PHP_EOL;
                    }
                }
            }

            if ($code != 200 && $code != 201) {
                $index = 0;
                $total = count($v);
                foreach ($v as $entry) {
                    $target_advertiser_name = $entry['target_advertiser_name'] ? '(' . $entry['target_advertiser_name'] . ')' : "";
                    $content[$code] .= '账户：' . $entry['target_advertiser_id'] . $target_advertiser_name . 
                                      ',失败原因：' . $entry['msg'];
                    if ($index < $total) {
                        $content[$code] .= PHP_EOL;
                    }
                }
            }
        }

        $list = [];
        foreach ($content as $code => $msg) {
            $list[$code] = [
                'code' => $code,
                'msg' => $msg
            ];
        }

        return $list;
    }

    /**
     * 加粉后账户自动充值 - 基于现有 CusCustomerUser::afterSave() 方法逻辑
     * 
     * @param string $subAdvertiserId 子账户ID
     * @return bool 是否成功
     */
    public function addFansRecharge($subAdvertiserId)
    {
        try {
            // 检查充值频次
            if (!$this->checkAddFansTransferFrequency($subAdvertiserId)) {
                return false;
            }

            $transferData = [
                'target_advertiser_ids' => [$subAdvertiserId],
                'amount' => 50,
                'user_name' => '系统自动充值',
            ];

            // 添加到队列
            TransferMoneyJobV2::addJob([
                'data' => $transferData,
                'execute_time' => time(),
                'isTimeRecharge' => false,
                'isSendMessage' => false
            ]);

            return true;
            
        } catch (Exception $e) {
            Yii::error('加粉后账户自动充值失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查加粉充值频次
     * 
     * @param string $subAdvertiserId 子账户ID
     * @return bool 是否允许充值
     */
    public function checkAddFansTransferFrequency($subAdvertiserId)
    {
        $today = date('Y-m-d');
        
        // 检查当天是否已被限制
        if ($this->cacheManager->isAddFansRestricted($today, $subAdvertiserId)) {
            return false;
        }

        // 检查5分钟内充值次数
        $count = $this->cacheManager->getAddFansTransferCount($subAdvertiserId);
        if ($count >= 5) {
            // 添加到当天限制列表
            $this->cacheManager->addAddFansRestrictedAccount($today, $subAdvertiserId);
            
            // 发送通知
            $error = '账户ID：' . $subAdvertiserId . PHP_EOL;
            $error .= '加粉异常,在一分钟内充值超过5次，已被限制充值';
            Yii::$app->feishuNotice->text($error);
            
            return false;
        }

        // 增加计数
        $this->cacheManager->incrementAddFansTransferCount($subAdvertiserId);
        return true;
    }

    /**
     * 获取当前平台适配器
     *
     * @return PlatformAdapterInterface
     * @throws Exception
     */
    private function getCurrentAdapter()
    {
        if ($this->currentAdapter === null) {
            $platformType = $this->platform == reportEnum::TIKTOL ? 'tiktok' : 'adq';
            $this->currentAdapter = $this->platformFactory->create($platformType);
        }
        
        return $this->currentAdapter;
    }

    /**
     * 保存充值记录到数据库
     * 
     * @return bool 是否保存成功
     * @throws Exception
     */
    private function saveTransferRecord()
    {
        try {
            $record = new AdsTransferMoneyRecord();
            
            // 生成充值序号
            $record->serial_number = $this->generateSerialNumber();
            
            // 设置基本信息
            $record->user_id = $this->user_id;
            $record->user_name = $this->user_name;
            $record->transfer_advertiser_id = $this->advertiser_id;
            $record->target_advertiser_id = $this->target_advertiser_id;
            $record->platform = $this->platform;
            $record->amount = $this->amount;
            $record->status = 1; // 1表示充值成功
            
            if (!$record->save()) {
                Yii::error('保存充值记录失败: ' . json_encode($record->errors), 'transfer_money_service');
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            Yii::error('保存充值记录异常: ' . $e->getMessage(), 'transfer_money_service');
            return false;
        }
    }

    /**
     * 生成充值序号
     * 
     * @return string 充值序号
     */
    private function generateSerialNumber()
    {
        $date = date('Ymd');
        $microtime = explode(' ', microtime());
        $microtime = $microtime[1] . substr($microtime[0], 2, 6);
        
        return 'TM' . $date . $microtime;
    }

    // Getter 和 Setter 方法
    
    /**
     * 获取平台工厂
     *
     * @return PlatformFactory
     */
    public function getPlatformFactory()
    {
        return $this->platformFactory;
    }

    /**
     * 获取缓存管理器
     *
     * @return TransferCacheManager
     */
    public function getCacheManager()
    {
        return $this->cacheManager;
    }

    /**
     * 获取错误码
     *
     * @return int
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * 设置错误码
     *
     * @param int $code
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * 获取成功码
     *
     * @return int
     */
    public function getSuccessCode()
    {
        return $this->success_code;
    }

    /**
     * 获取时间码
     *
     * @return int
     */
    public function getTimeCode()
    {
        return $this->time_code;
    }

    /**
     * 获取成功但余额不足码
     *
     * @return int
     */
    public function getSuccessInsufficientBalanceCode()
    {
        return $this->success_insufficient_balance_code;
    }

    /**
     * 获取技术错误码
     *
     * @return int
     */
    public function getErrorCodeIt()
    {
        return $this->error_code_it;
    }

    /**
     * 获取推广错误码
     *
     * @return int
     */
    public function getErrorCodePromote()
    {
        return $this->error_code_promote;
    }

    /**
     * 获取余额不足错误码
     *
     * @return int
     */
    public function getErrorCodeInsufficientBalance()
    {
        return $this->error_code_insufficient_balance;
    }

    /**
     * 获取目标账户ID
     *
     * @return string
     */
    public function getTargetAdvertiserId()
    {
        return $this->target_advertiser_id;
    }

    /**
     * 设置目标账户ID
     *
     * @param string $targetAdvertiserId
     */
    public function setTargetAdvertiserId($targetAdvertiserId)
    {
        $this->target_advertiser_id = $targetAdvertiserId;
    }

    /**
     * 获取主体
     *
     * @return string
     */
    public function getMainBody()
    {
        return $this->mainBody;
    }

    /**
     * 获取广告主ID
     *
     * @return string
     */
    public function getAdvertiserId()
    {
        return $this->advertiser_id;
    }

    /**
     * 设置广告主ID
     *
     * @param string $advertiserId
     */
    public function setAdvertiserId($advertiserId)
    {
        $this->advertiser_id = $advertiserId;
    }

    /**
     * 获取金额
     *
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * 设置金额
     *
     * @param float $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * 获取用户名
     *
     * @return string
     */
    public function getUserName()
    {
        return $this->user_name;
    }

    /**
     * 设置用户名
     *
     * @param string $userName
     */
    public function setUserName($userName)
    {
        $this->user_name = $userName;
    }

    /**
     * 获取余额不足金额
     *
     * @return float
     */
    public function getInsufficientBalance()
    {
        return $this->insufficientBalance;
    }

    /**
     * 设置余额不足金额
     *
     * @param float $insufficientBalance
     */
    public function setInsufficientBalance($insufficientBalance)
    {
        $this->insufficientBalance = $insufficientBalance;
    }

    /**
     * 获取定时充值时间
     *
     * @return string
     */
    public function getTimeRecharge()
    {
        return $this->timeRecharge;
    }

    /**
     * 是否启用定时充值
     *
     * @return bool
     */
    public function isTimeRechargeEnabled()
    {
        return !empty($this->timeRecharge);
    }

    /**
     * 获取平台
     *
     * @return string
     */
    public function getPlatform()
    {
        return $this->platform;
    }
}